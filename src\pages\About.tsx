import { Download, GraduationCap, Briefcase, Award, Languages, Loader2 } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from 'react';
import html2canvas from 'html2canvas';

const About = () => {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // 🎯 دالة إنشاء PDF متقدمة مع دعم عربي كامل باستخدام html2canvas
  const generatePDF = async () => {
    setIsGeneratingPDF(true);

    try {
      // إنشاء عنصر HTML مخصص للسيرة الذاتية
      const resumeElement = document.createElement('div');
      resumeElement.innerHTML = getArabicResumeHTML();
      resumeElement.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 794px;
        background: white;
        color: #333;
        font-family: 'Ta<PERSON>a', 'Aria<PERSON>', 'Segoe UI', sans-serif;
        direction: rtl;
        text-align: right;
        padding: 40px;
        box-sizing: border-box;
        line-height: 1.6;
        font-size: 14px;
      `;

      document.body.appendChild(resumeElement);

      // انتظار تحميل الخطوط
      await new Promise(resolve => setTimeout(resolve, 500));

      // تحويل HTML إلى Canvas باستخدام html2canvas مع إعدادات محسنة
      const canvas = await html2canvas(resumeElement, {
        scale: 3, // جودة عالية
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794,
        height: 1123,
        scrollX: 0,
        scrollY: 0,
        logging: false,
        imageTimeout: 0
      });

      // إزالة العنصر المؤقت
      document.body.removeChild(resumeElement);

      // تحويل Canvas إلى صورة وتحميلها
      const link = document.createElement('a');
      link.download = `Hodifa_AlHodify_CV_${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL('image/png', 1.0);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // إظهار رسالة نجاح
      alert('✅ تم تحميل السيرة الذاتية بصيغة PNG بنجاح!\n\nيمكنك طباعتها أو تحويلها إلى PDF باستخدام أي أداة تحويل.');
    } catch (error) {
      console.error('خطأ في إنشاء الملف:', error);

      // في حالة الفشل، استخدم الطريقة البديلة
      try {
        await generatePrintableHTML();
      } catch (htmlError) {
        console.error('خطأ في HTML:', htmlError);
        alert('❌ حدث خطأ في إنشاء الملف. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // 🎨 دالة إنشاء HTML محسن للعربية
  const getArabicResumeHTML = () => {
    return `
      <div style="font-family: 'Tahoma', 'Arial', sans-serif; direction: rtl; text-align: right; color: #333; line-height: 1.6;">
        <!-- رأس الصفحة -->
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #f59e0b; padding-bottom: 20px;">
          <h1 style="color: #f59e0b; font-size: 28px; margin: 0 0 10px 0; font-weight: bold;">
            حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
          </h1>
          <h2 style="color: #666; font-size: 18px; margin: 0 0 15px 0; font-weight: normal;">
            مهندس تقنية معلومات
          </h2>
          <div style="font-size: 14px; color: #333;">
            <p style="margin: 5px 0;">📧 <EMAIL></p>
            <p style="margin: 5px 0;">📱 +967 777548421 / +967 718706242</p>
            <p style="margin: 5px 0;">📍 عدن، اليمن</p>
          </div>
        </div>

        <!-- الملخص المهني -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #f59e0b; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f59e0b; padding-bottom: 5px;">
            الملخص المهني
          </h3>
          <p style="text-align: justify; line-height: 1.8; margin: 0;">
            طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: TypeScript, JavaScript, HTML5, CSS3, C++, C#, PHP, SQL كما أنني قد عملت مسبقاً في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حالياً إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على SQL Server و MySQL وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضاً معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.
          </p>
        </div>

        <!-- التعليم والخبرة -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 25px;">
          <div>
            <h3 style="color: #f59e0b; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f59e0b; padding-bottom: 5px;">التعليم</h3>
            <div style="border-right: 3px solid #f59e0b; padding-right: 15px;">
              <h4 style="font-weight: bold; margin: 0 0 5px 0;">جامعة عدن - كلية الهندسة</h4>
              <p style="margin: 0; color: #666;">بكالوريوس تقنية المعلومات - السنة الرابعة</p>
            </div>
          </div>
          <div>
            <h3 style="color: #f59e0b; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f59e0b; padding-bottom: 5px;">اللغات</h3>
            <div style="border-right: 3px solid #f59e0b; padding-right: 15px;">
              <p style="margin: 5px 0;"><strong>العربية:</strong> الأم</p>
              <p style="margin: 5px 0;"><strong>الإنجليزية:</strong> ممتاز</p>
              <p style="margin: 5px 0;"><strong>الفرنسية:</strong> متوسط</p>
            </div>
          </div>
        </div>

        <!-- الخبرة العملية -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #f59e0b; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f59e0b; padding-bottom: 5px;">الخبرة العملية</h3>
          <div style="border-right: 3px solid #f59e0b; padding-right: 15px; margin-bottom: 10px;">
            <p style="margin: 0;">عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية</p>
          </div>
          <div style="border-right: 3px solid #f59e0b; padding-right: 15px;">
            <p style="margin: 0;">أعمل في مجال التجارة الحرة</p>
          </div>
        </div>

        <!-- المشاريع -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #f59e0b; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f59e0b; padding-bottom: 5px;">المشاريع</h3>
          <div style="border-right: 3px solid #f59e0b; padding-right: 15px; margin-bottom: 10px;">
            <h4 style="color: #f59e0b; margin: 0 0 5px 0;">برنامج إدارة الحجز</h4>
            <p style="margin: 0;">برنامج desktop لإدارة عمليات الحجز</p>
          </div>
          <div style="border-right: 3px solid #f59e0b; padding-right: 15px; margin-bottom: 10px;">
            <h4 style="color: #f59e0b; margin: 0 0 5px 0;">متجر إلكتروني - Laravel 11</h4>
            <p style="margin: 0;">متجر للأطفال حديثي الولادة</p>
          </div>
          <div style="border-right: 3px solid #f59e0b; padding-right: 15px; margin-bottom: 10px;">
            <h4 style="color: #f59e0b; margin: 0 0 5px 0;">تصميم قواعد البيانات</h4>
            <p style="margin: 0;">للمدارس والفنادق</p>
          </div>
          <div style="border-right: 3px solid #f59e0b; padding-right: 15px;">
            <h4 style="color: #f59e0b; margin: 0 0 5px 0;">الموقع الشخصي</h4>
            <p style="margin: 0;">React 18, TypeScript, Tailwind CSS</p>
          </div>
        </div>

        <!-- المهارات التقنية -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #f59e0b; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f59e0b; padding-bottom: 5px;">المهارات التقنية</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
            <div>
              <h4 style="color: #f59e0b; margin: 0 0 10px 0;">لغات البرمجة</h4>
              <ul style="margin: 0; padding-right: 20px;">
                <li>TypeScript</li>
                <li>JavaScript</li>
                <li>HTML5 & CSS3</li>
                <li>C++ & C#</li>
                <li>PHP & SQL</li>
              </ul>
            </div>
            <div>
              <h4 style="color: #f59e0b; margin: 0 0 10px 0;">Frontend</h4>
              <ul style="margin: 0; padding-right: 20px;">
                <li>React 18</li>
                <li>React Query</li>
                <li>Tailwind CSS</li>
                <li>ShadCN/UI</li>
              </ul>
            </div>
            <div>
              <h4 style="color: #f59e0b; margin: 0 0 10px 0;">أدوات التطوير</h4>
              <ul style="margin: 0; padding-right: 20px;">
                <li>Vite & Node.js</li>
                <li>Git & GitHub</li>
                <li>Laravel Framework</li>
                <li>MySQL & SQL Server</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- الدورات التدريبية -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #f59e0b; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f59e0b; padding-bottom: 5px;">الدورات التدريبية</h3>
          <div style="border-right: 3px solid #f59e0b; padding-right: 15px;">
            <p style="margin: 5px 0;">• أساسيات البرمجة و C++ - منصة Programming Advices (2022-2024)</p>
            <p style="margin: 5px 0;">• تطوير تطبيقات سطح المكتب - منصة Programming Advices (2024)</p>
            <p style="margin: 5px 0;">• تطوير المواقع Frontend - منصة Alzero Web School</p>
            <p style="margin: 5px 0;">• تطوير المواقع Backend - أكاديمية الجيل العربي</p>
            <p style="margin: 5px 0;">• اللغة الإنجليزية - معهد أميدست الأمريكي (سنة كاملة)</p>
            <p style="margin: 5px 0;">• CCNA - سبتمبر 2023</p>
            <p style="margin: 5px 0;">• CPS - معهد أميديست الأمريكي</p>
          </div>
        </div>

        <!-- تذييل -->
        <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
          تم إنشاء هذه السيرة الذاتية في: ${new Date().toLocaleDateString('ar-EG')}
        </div>
      </div>
    `;
  };

  // 🎯 الطريقة البديلة: إنشاء HTML للطباعة
  const generatePrintableHTML = async () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('فشل في فتح نافذة الطباعة');
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>السيرة الذاتية - حذيفه عبدالمعز الحذيفي</title>
        <style>
          @page { size: A4; margin: 20mm; }
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
          }
          @media print { body { print-color-adjust: exact; } }
        </style>
      </head>
      <body>
        ${getArabicResumeHTML()}
      </body>
      </html>
    `;

    printWindow.document.body.innerHTML = htmlContent;

    setTimeout(() => {
      printWindow.print();
      alert('✅ تم فتح نافذة الطباعة بنجاح!');
    }, 1000);
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">السيرة</span> الذاتية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <Button
              onClick={generatePDF}
              disabled={isGeneratingPDF}
              className="bg-amber-500 hover:bg-amber-600 disabled:bg-amber-400 disabled:cursor-not-allowed text-black font-semibold px-8 py-3 rounded-full transition-all duration-300"
            >
              {isGeneratingPDF ? (
                <>
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  جاري إنشاء الملف...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 ml-2" />
                  تحميل السيرة الذاتية
                </>
              )}
            </Button>
          </div>

          {/* Personal Info with Profile Photo */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <div className="flex flex-col items-center space-y-6">
                {/* Profile Photo */}
                <div className="relative w-32 h-32 group">
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-500/30 to-amber-600/30 rounded-full animate-pulse"></div>
                  <div className="absolute inset-1 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full"></div>
                  <div className="absolute inset-2 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-500">
                    <img
                      src="/profile-photo.jpg"
                      alt="حذيفه عبدالمعز الحذيفي"
                      className="w-full h-full object-cover transition-all duration-500 group-hover:brightness-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-amber-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                </div>

                <div className="text-center">
                  <CardTitle className="text-2xl text-amber-400">
                    حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
                  </CardTitle>
                  <p className="text-xl text-gray-300 mt-2">مهندس تقنية معلومات</p>
                </div>
              </div>
            </CardHeader>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الملخص المهني
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 leading-relaxed">
                طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: 
                C++, C#, Html, Css, Js, Php, Sql كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على Sqlserver و Mysql وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضا معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.
              </p>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <GraduationCap className="w-5 h-5 ml-2" />
                  التعليم
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-200">جامعة عدن - كلية الهندسة</h3>
                    <p className="text-gray-400">طالب بكلاريوس – تقنية المعلومات</p>
                    <p className="text-gray-400">السنة الرابعة</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <Languages className="w-5 h-5 ml-2" />
                  اللغات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">العربية</span>
                    <span className="text-amber-400">الأم</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الإنجليزية</span>
                    <span className="text-amber-400">ممتاز</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الفرنسية</span>
                    <span className="text-amber-400">متوسط</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الخبرة العملية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">كما أنني أعمل في مجال التجارة الحرة.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المشاريع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">برنامج إدارة الحجز</h4>
                  <p className="text-gray-300">بناء برنامج desktop حر وبسيط في عمليات الحجز وفكرة البرنامج شاملة لأي جانب يتطلب إدارة الحجز بشكل عام.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">متجر إلكتروني - Laravel 11</h4>
                  <p className="text-gray-300">بناء موقع تجاري حر بإستخدام بيئة التعامل Laravel11 وقد خصصت الموقع أن يكون متجر يلبي احتياجات الأطفال حديثي الولادة حتى سن السنتين ويجري التطوير عليه إلى أن يكون الموقع أكثر مرونة لأي جانب تجاري محتمل.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">تصميم قواعد البيانات للمدارس</h4>
                  <p className="text-gray-300">عملت كشريك في تحليل وتصميم قواعد بيانات لأحدى مدارس القرى اليمنية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">نظام إدارة الفنادق</h4>
                  <p className="text-gray-300">تحليل وتصميم قاعدة بيانات لأحد الفنادق في عدن</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Award className="w-5 h-5 ml-2" />
                الدورات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">أساسيات البرمجة و C++</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2022-2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير تطبيقات سطح المكتب</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Frontend</h4>
                  <p className="text-gray-400">منصة Alzero Web School المصرية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Backend</h4>
                  <p className="text-gray-400">أكاديمية الجيل العربي - اليمن</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">اللغة الإنجليزية</h4>
                  <p className="text-gray-400">معهد أميدست الأمريكي - عدن (سنة كاملة)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CCNA</h4>
                  <p className="text-gray-400">سبتمبر 2023</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CPS</h4>
                  <p className="text-gray-400">معهد أميديست الأمريكي</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المهارات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">لغات البرمجة</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• TypeScript</li>
                    <li>• JavaScript (ES6+)</li>
                    <li>• HTML5</li>
                    <li>• CSS3</li>
                    <li>• C++</li>
                    <li>• C#</li>
                    <li>• PHP</li>
                    <li>• SQL</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">Frontend التقنيات</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• React 18</li>
                    <li>• React Router DOM</li>
                    <li>• React Query (TanStack)</li>
                    <li>• Tailwind CSS</li>
                    <li>• Radix UI</li>
                    <li>• ShadCN/UI</li>
                    <li>• Lucide React Icons</li>
                    <li>• Next Themes</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">أدوات التطوير</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• Vite</li>
                    <li>• Node.js</li>
                    <li>• npm/yarn</li>
                    <li>• ESLint</li>
                    <li>• PostCSS</li>
                    <li>• Autoprefixer</li>
                    <li>• Git & GitHub</li>
                    <li>• GitHub Actions</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">Backend & قواعد البيانات</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• Laravel Framework</li>
                    <li>• MySQL</li>
                    <li>• SQL Server</li>
                    <li>• RESTful APIs</li>
                    <li>• NewsData API</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">التصميم & UX/UI</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• Figma</li>
                    <li>• Responsive Design</li>
                    <li>• Dark/Light Mode</li>
                    <li>• RTL Support</li>
                    <li>• CSS Animations</li>
                    <li>• Component Design</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">المهارات الشخصية</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• سريع التعلم</li>
                    <li>• مهارة التواصل</li>
                    <li>• العمل ضمن فريق</li>
                    <li>• حل المشاكل</li>
                    <li>• إدارة المشاريع</li>
                    <li>• مواكبة التقنيات الحديثة</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-amber-500/20">
                <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">مشروع الموقع الشخصي - التقنيات المستخدمة</h4>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <p className="text-gray-300 text-sm leading-relaxed">
                    تم بناء هذا الموقع باستخدام <span className="text-amber-400 font-semibold">React 18</span> مع
                    <span className="text-amber-400 font-semibold"> TypeScript</span> و
                    <span className="text-amber-400 font-semibold"> Vite</span> كأداة البناء،
                    <span className="text-amber-400 font-semibold"> Tailwind CSS</span> للتصميم المتجاوب،
                    <span className="text-amber-400 font-semibold"> React Query</span> لإدارة البيانات،
                    <span className="text-amber-400 font-semibold"> ShadCN/UI</span> للمكونات،
                    ونشر تلقائي على <span className="text-amber-400 font-semibold">GitHub Pages</span> باستخدام
                    <span className="text-amber-400 font-semibold"> GitHub Actions</span>.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default About;
